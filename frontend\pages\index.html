<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .file-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .folder-tree-item {
            transition: all 0.2s ease;
        }

        .folder-tree-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .context-menu {
            position: absolute;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            min-width: 150px;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .context-menu-item:hover {
            background-color: #f3f4f6;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 16px;
        }

        @media (min-width: 640px) {
            .file-grid {
                grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
            }
        }

        @media (min-width: 1024px) {
            .file-grid {
                grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            }
        }

        /* Drag and Drop Styles */
        .dragging {
            opacity: 0.5;
            transform: rotate(5deg);
            z-index: 1000;
        }

        .drag-over {
            background-color: #dbeafe !important;
            border: 2px dashed #3b82f6 !important;
            transform: scale(1.05);
        }

        .drag-over .folder-icon {
            color: #3b82f6 !important;
        }

        .drop-zone {
            position: relative;
        }

        .drop-zone::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(59, 130, 246, 0.1);
            border: 2px dashed #3b82f6;
            border-radius: 12px;
            opacity: 0;
            transition: opacity 0.2s;
            pointer-events: none;
        }

        .drop-zone.drag-over::after {
            opacity: 1;
        }

        /* Upload Drop Zone */
        .upload-drop-zone {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            background: #f9fafb;
            transition: all 0.3s ease;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
        }

        .upload-drop-zone:hover,
        .upload-drop-zone.drag-over {
            border-color: #3b82f6;
            background: #eff6ff;
            transform: scale(1.02);
        }

        .upload-drop-zone.drag-over {
            border-color: #1d4ed8;
            background: #dbeafe;
        }

        /* Fix bottom spacing */
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .main-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .content-area {
            flex: 1;
            padding-bottom: 2rem;
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-900 text-white shadow-xl">
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center">
                    <i class="fas fa-folder-open text-blue-400 text-2xl mr-3"></i>
                    <h1 class="text-xl font-bold">File Manager</h1>
                </div>
            </div>
            <nav id="folder-tree" class="p-4 overflow-y-auto h-full">
                <!-- Folder tree will be dynamically generated here -->
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top bar -->
            <header class="bg-white shadow-lg border-b border-gray-200 p-4 flex justify-between items-center z-10">
                <div class="flex items-center flex-1">
                    <!-- Breadcrumbs will be inserted here by JS -->
                    <nav id="breadcrumb-nav" class="flex items-center" aria-label="Breadcrumb">
                        <i class="fas fa-home text-gray-500 mr-2"></i>
                    </nav>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- View Toggle -->
                    <div class="flex bg-gray-100 rounded-lg p-1">
                        <button id="grid-view-btn"
                            class="px-3 py-1 rounded-md bg-white shadow-sm text-gray-700 hover:bg-gray-50">
                            <i class="fas fa-th text-sm"></i>
                        </button>
                        <button id="list-view-btn" class="px-3 py-1 rounded-md text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-list text-sm"></i>
                        </button>
                    </div>

                    <!-- Search -->
                    <div class="relative">
                        <input type="search" placeholder="Search files..."
                            class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-64">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>

                    <!-- Action Buttons -->
                    <button id="create-folder-button"
                        class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                        <i class="fas fa-folder-plus mr-2"></i>
                        <span>New Folder</span>
                    </button>
                    <button id="upload-file-button"
                        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg inline-flex items-center transition-colors">
                        <i class="fas fa-upload mr-2"></i>
                        <span>Upload</span>
                    </button>
                    <input type="file" id="file-input" multiple class="hidden">
                    <button id="logout-button"
                        class="bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </button>
                </div>
            </header>

            <!-- File and folder grid -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <div id="document-list" class="file-grid">
                    <!-- File and folder items will be dynamically generated here -->
                </div>

                <!-- Upload Drop Zone (shown when empty or on drag) -->
                <div id="upload-drop-zone" class="upload-drop-zone hidden mt-6">
                    <div class="text-center">
                        <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Drop files here to upload</h3>
                        <p class="text-gray-500 mb-4">or click to browse files</p>
                        <button class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                            Choose Files
                        </button>
                    </div>
                </div>

                <!-- Context Menu -->
                <div id="context-menu" class="context-menu hidden">
                    <div class="context-menu-item" data-action="open">
                        <i class="fas fa-folder-open mr-2"></i>Open
                    </div>
                    <div class="context-menu-item" data-action="rename">
                        <i class="fas fa-edit mr-2"></i>Rename
                    </div>
                    <div class="context-menu-item" data-action="move">
                        <i class="fas fa-arrows-alt mr-2"></i>Move
                    </div>
                    <div class="context-menu-item" data-action="delete">
                        <i class="fas fa-trash mr-2"></i>Delete
                    </div>
                    <div class="context-menu-item" data-action="share">
                        <i class="fas fa-share mr-2"></i>Share
                    </div>
                    <div class="context-menu-item" data-action="download">
                        <i class="fas fa-download mr-2"></i>Download
                    </div>
                    <div class="context-menu-item" data-action="properties">
                        <i class="fas fa-info-circle mr-2"></i>Properties
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <div id="password-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Enter Password</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="password" id="modal-password" class="px-4 py-2 border rounded-md w-full">
                </div>
                <div class="items-center px-4 py-3">
                    <button id="modal-password-submit"
                        class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Submit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="share-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Share File</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="text" id="modal-share-link" class="px-4 py-2 border rounded-md w-full" readonly>
                </div>
                <div class="items-center px-4 py-3">
                    <button id="modal-share-copy"
                        class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Copy Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Properties Modal -->
    <div id="properties-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-10 mx-auto p-6 border w-96 shadow-lg rounded-lg bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Properties</h3>
                <button id="close-properties-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="properties-content" class="space-y-3">
                <!-- Properties content will be populated by JavaScript -->
            </div>
            <div class="mt-6 flex justify-end">
                <button id="properties-ok-btn"
                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    OK
                </button>
            </div>
        </div>
    </div>

    <!-- Move Modal -->
    <div id="move-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-10 mx-auto p-6 border w-96 shadow-lg rounded-lg bg-white">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Move Item</h3>
                <button id="close-move-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Select destination folder:</label>
                <div id="folder-tree-modal" class="border rounded-lg p-3 max-h-64 overflow-y-auto bg-gray-50">
                    <!-- Folder tree for selection will be populated by JavaScript -->
                </div>
            </div>
            <div class="flex justify-end space-x-3">
                <button id="move-cancel-btn"
                    class="px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-400">
                    Cancel
                </button>
                <button id="move-confirm-btn"
                    class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    Move
                </button>
            </div>
        </div>
    </div>

    <script src="../js/app.js"></script>
</body>

</html>