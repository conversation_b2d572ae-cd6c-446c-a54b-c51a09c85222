<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DMS</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen bg-gray-200">
        <!-- Sidebar -->
        <div class="w-64 bg-gray-800 text-white p-5">
            <h1 class="text-2xl font-bold mb-5">DMS</h1>
            <nav id="folder-tree">
                <!-- Folder tree will be dynamically generated here -->
            </nav>
        </div>

        <!-- Main content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Top bar -->
            <header class="bg-white shadow-md p-4 flex justify-between items-center z-10">
                <div class="flex items-center">
                    <!-- Breadcrumbs will be inserted here by JS -->
                    <nav id="breadcrumb-nav" class="flex" aria-label="Breadcrumb">
                    </nav>
                </div>
                <div class="flex items-center">
                    <input type="search" placeholder="Search files..." class="px-4 py-2 border rounded-md mr-4 w-64">
                    <button id="create-folder-button" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded inline-flex items-center mr-2">
                        <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h5l2 2h5a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" /></svg>
                        <span>Create Folder</span>
                    </button>
                    <button id="upload-file-button" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded inline-flex items-center mr-4">
                        <svg class="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
                        <span>Upload</span>
                    </button>
                    <button id="logout-button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Logout
                    </button>
                </div>
            </header>
            
            <!-- File and folder grid -->
            <main class="flex-1 p-6 overflow-y-auto bg-gray-200">
                <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6" id="document-list">
                    <!-- File and folder items will be dynamically generated here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Modals -->
    <div id="password-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Enter Password</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="password" id="modal-password" class="px-4 py-2 border rounded-md w-full">
                </div>
                <div class="items-center px-4 py-3">
                    <button id="modal-password-submit" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Submit
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="share-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Share File</h3>
                <div class="mt-2 px-7 py-3">
                    <input type="text" id="modal-share-link" class="px-4 py-2 border rounded-md w-full" readonly>
                </div>
                <div class="items-center px-4 py-3">
                    <button id="modal-share-copy" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Copy Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="../js/app.js"></script>
</body>
</html>