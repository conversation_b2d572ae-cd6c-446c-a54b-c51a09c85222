document.addEventListener("DOMContentLoaded", () => {
  // Admin Panel State Management
  let currentSection = "dashboard";
  let users = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "user",
      status: "active",
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "user",
      status: "inactive",
    },
    {
      id: 4,
      name: "<PERSON>",
      email: "<EMAIL>",
      role: "moderator",
      status: "active",
    },
  ];

  let permissions = [
    {
      id: 1,
      folder: "Documents",
      user: "<PERSON>",
      permission: "read",
      type: "user",
    },
    {
      id: 2,
      folder: "Documents/Confidential",
      user: "Admin Group",
      permission: "admin",
      type: "group",
    },
    {
      id: 3,
      folder: "Images",
      user: "<PERSON>",
      permission: "write",
      type: "user",
    },
    {
      id: 4,
      folder: "Videos",
      user: "All Users",
      permission: "read",
      type: "group",
    },
    {
      id: 5,
      folder: "Archive",
      user: "<PERSON>",
      permission: "none",
      type: "user",
    },
  ];

  let privacySettings = {
    defaultFolderVisibility: "private",
    allowPublicSharing: true,
    requirePasswordForSharing: false,
    maxShareDuration: 30,
    allowAnonymousAccess: false,
    enableAuditLogging: true,
    dataRetentionDays: 365,
  };

  let auditLogs = [
    {
      id: 1,
      timestamp: "2024-01-15 10:30:00",
      user: "<EMAIL>",
      action: "folder_access",
      resource: "Documents/Reports",
      ip: "*************",
    },
    {
      id: 2,
      timestamp: "2024-01-15 09:45:00",
      user: "<EMAIL>",
      action: "permission_change",
      resource: "Images",
      ip: "***********01",
    },
    {
      id: 3,
      timestamp: "2024-01-15 08:20:00",
      user: "admin",
      action: "user_create",
      resource: "<EMAIL>",
      ip: "***********",
    },
    {
      id: 4,
      timestamp: "2024-01-14 16:15:00",
      user: "<EMAIL>",
      action: "file_upload",
      resource: "Videos/presentation.mp4",
      ip: "*************",
    },
  ];

  // Navigation Elements
  const navItems = document.querySelectorAll(".admin-nav-item");
  const sections = document.querySelectorAll(".admin-section");
  const pageTitle = document.getElementById("page-title");
  const logoutBtn = document.getElementById("logout-admin");

  // Initialize Admin Panel
  function initializeAdminPanel() {
    setupNavigation();
    renderDashboard();
    renderPermissions();
    setupEventListeners();
  }

  // Navigation Setup
  function setupNavigation() {
    navItems.forEach((item) => {
      item.addEventListener("click", (e) => {
        e.preventDefault();
        const section = item.getAttribute("href").substring(1);
        switchSection(section);
      });
    });
  }

  function switchSection(section) {
    // Update navigation active state
    navItems.forEach((item) => {
      item.classList.remove("active");
      if (item.getAttribute("href") === `#${section}`) {
        item.classList.add("active");
      }
    });

    // Hide all sections
    sections.forEach((sec) => sec.classList.add("hidden"));

    // Show selected section
    const targetSection = document.getElementById(`${section}-section`);
    if (targetSection) {
      targetSection.classList.remove("hidden");
    }

    // Update page title
    const titles = {
      dashboard: "Dashboard",
      permissions: "Folder Permissions",
      users: "User Management",
      privacy: "Privacy Settings",
      audit: "Audit Logs",
      settings: "System Settings",
    };
    pageTitle.textContent = titles[section] || "Admin Panel";
    currentSection = section;

    // Render section content
    switch (section) {
      case "permissions":
        renderPermissions();
        break;
      case "users":
        renderUsers();
        break;
      case "privacy":
        renderPrivacySettings();
        break;
      case "audit":
        renderAuditLogs();
        break;
      case "settings":
        renderSystemSettings();
        break;
    }
  }

  // Dashboard Rendering
  function renderDashboard() {
    // Dashboard is static HTML, no dynamic rendering needed for now
    // Could add real-time stats updates here
  }

  // Permissions Management
  function renderPermissions() {
    const tableBody = document.getElementById("permissions-table-body");
    if (!tableBody) return;

    tableBody.innerHTML = permissions
      .map(
        (perm) => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="fas fa-folder text-blue-500 mr-2"></i>
            <span class="text-sm font-medium text-gray-900">${
              perm.folder
            }</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <i class="fas ${
              perm.type === "user" ? "fa-user" : "fa-users"
            } text-gray-400 mr-2"></i>
            <span class="text-sm text-gray-900">${perm.user}</span>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge permission-${perm.permission}">
            ${
              perm.permission.charAt(0).toUpperCase() + perm.permission.slice(1)
            }
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="editPermission(${
            perm.id
          })" class="text-blue-600 hover:text-blue-900 mr-3">
            <i class="fas fa-edit"></i>
          </button>
          <button onclick="deletePermission(${
            perm.id
          })" class="text-red-600 hover:text-red-900">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      </tr>
    `
      )
      .join("");
  }

  // User Management
  function renderUsers() {
    const usersSection = document.getElementById("users-section");
    if (!usersSection) {
      createUsersSection();
    }

    const tableBody = document.getElementById("users-table-body");
    if (!tableBody) return;

    tableBody.innerHTML = users
      .map(
        (user) => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap">
          <div class="flex items-center">
            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
              <i class="fas fa-user text-gray-600"></i>
            </div>
            <div class="ml-4">
              <div class="text-sm font-medium text-gray-900">${user.name}</div>
              <div class="text-sm text-gray-500">${user.email}</div>
            </div>
          </div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge ${getRoleBadgeClass(user.role)}">
            ${user.role.charAt(0).toUpperCase() + user.role.slice(1)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge ${
            user.status === "active" ? "permission-read" : "permission-none"
          }">
            ${user.status.charAt(0).toUpperCase() + user.status.slice(1)}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button onclick="editUser(${
            user.id
          })" class="text-blue-600 hover:text-blue-900 mr-3">
            <i class="fas fa-edit"></i>
          </button>
          <button onclick="deleteUser(${
            user.id
          })" class="text-red-600 hover:text-red-900">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      </tr>
    `
      )
      .join("");
  }

  function getRoleBadgeClass(role) {
    switch (role) {
      case "admin":
        return "permission-admin";
      case "moderator":
        return "permission-write";
      case "user":
        return "permission-read";
      default:
        return "permission-none";
    }
  }

  // Privacy Settings
  function renderPrivacySettings() {
    const privacySection = document.getElementById("privacy-section");
    if (!privacySection) {
      createPrivacySection();
    }
  }

  // Audit Logs
  function renderAuditLogs() {
    const auditSection = document.getElementById("audit-section");
    if (!auditSection) {
      createAuditSection();
    }
  }

  // System Settings
  function renderSystemSettings() {
    const settingsSection = document.getElementById("settings-section");
    if (!settingsSection) {
      createSystemSettingsSection();
    }
  }

  // Event Listeners
  function setupEventListeners() {
    // Logout functionality
    if (logoutBtn) {
      logoutBtn.addEventListener("click", () => {
        if (confirm("Are you sure you want to logout?")) {
          window.location.href = "login.html";
        }
      });
    }

    // Add Permission Button
    const addPermissionBtn = document.getElementById("add-permission-btn");
    if (addPermissionBtn) {
      addPermissionBtn.addEventListener("click", showAddPermissionModal);
    }
  }

  // Permission Management Functions
  window.editPermission = function (id) {
    const permission = permissions.find((p) => p.id === id);
    if (permission) {
      showEditPermissionModal(permission);
    }
  };

  window.deletePermission = function (id) {
    if (confirm("Are you sure you want to delete this permission?")) {
      permissions = permissions.filter((p) => p.id !== id);
      renderPermissions();
    }
  };

  // User Management Functions
  window.editUser = function (id) {
    const user = users.find((u) => u.id === id);
    if (user) {
      showEditUserModal(user);
    }
  };

  window.deleteUser = function (id) {
    if (confirm("Are you sure you want to delete this user?")) {
      users = users.filter((u) => u.id !== id);
      renderUsers();
    }
  };

  // Modal Functions (to be implemented)
  function showAddPermissionModal() {
    // Implementation for add permission modal
    console.log("Show add permission modal");
  }

  function showEditPermissionModal(permission) {
    // Implementation for edit permission modal
    console.log("Show edit permission modal", permission);
  }

  function showEditUserModal(user) {
    // Implementation for edit user modal
    console.log("Show edit user modal", user);
  }

  // Create missing sections dynamically
  function createUsersSection() {
    const main = document.querySelector("main");
    const usersSection = document.createElement("div");
    usersSection.id = "users-section";
    usersSection.className = "admin-section hidden";
    usersSection.innerHTML = `
      <div class="admin-card p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-800">User Management</h3>
          <button id="add-user-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Add User
          </button>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody id="users-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
          </table>
        </div>
      </div>
    `;
    main.appendChild(usersSection);
  }

  function createPrivacySection() {
    const main = document.querySelector("main");
    const privacySection = document.createElement("div");
    privacySection.id = "privacy-section";
    privacySection.className = "admin-section hidden";
    privacySection.innerHTML = `
      <div class="admin-card p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-6">Privacy & Security Settings</h3>

        <div class="space-y-6">
          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Default Folder Visibility</h4>
              <p class="text-sm text-gray-600">Set default visibility for new folders</p>
            </div>
            <select id="default-visibility" class="px-3 py-2 border border-gray-300 rounded-md">
              <option value="private">Private</option>
              <option value="public">Public</option>
              <option value="restricted">Restricted</option>
            </select>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Allow Public Sharing</h4>
              <p class="text-sm text-gray-600">Enable users to create public share links</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="allow-public-sharing" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Require Password for Sharing</h4>
              <p class="text-sm text-gray-600">Force password protection on shared links</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="require-password" class="sr-only peer">
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Enable Audit Logging</h4>
              <p class="text-sm text-gray-600">Track all user actions and file access</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="enable-audit" class="sr-only peer" checked>
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div>
              <h4 class="font-medium text-gray-900">Data Retention (Days)</h4>
              <p class="text-sm text-gray-600">How long to keep deleted files in trash</p>
            </div>
            <input type="number" id="retention-days" value="365" min="1" max="3650" class="px-3 py-2 border border-gray-300 rounded-md w-20">
          </div>
        </div>

        <div class="mt-6 flex justify-end">
          <button id="save-privacy-settings" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
            <i class="fas fa-save mr-2"></i>
            Save Settings
          </button>
        </div>
      </div>
    `;
    main.appendChild(privacySection);
  }

  function createAuditSection() {
    const main = document.querySelector("main");
    const auditSection = document.createElement("div");
    auditSection.id = "audit-section";
    auditSection.className = "admin-section hidden";
    auditSection.innerHTML = `
      <div class="admin-card p-6 mb-6">
        <div class="flex justify-between items-center mb-6">
          <h3 class="text-lg font-semibold text-gray-800">Audit Logs</h3>
          <div class="flex space-x-2">
            <input type="date" id="audit-date-filter" class="px-3 py-2 border border-gray-300 rounded-md">
            <button id="export-audit-logs" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
              <i class="fas fa-download mr-2"></i>
              Export
            </button>
          </div>
        </div>

        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Resource</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
              </tr>
            </thead>
            <tbody id="audit-table-body" class="bg-white divide-y divide-gray-200">
            </tbody>
          </table>
        </div>
      </div>
    `;
    main.appendChild(auditSection);

    // Populate audit logs
    const auditTableBody = document.getElementById("audit-table-body");
    auditTableBody.innerHTML = auditLogs
      .map(
        (log) => `
      <tr>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${
          log.timestamp
        }</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${
          log.user
        }</td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="permission-badge ${getActionBadgeClass(log.action)}">
            ${log.action.replace("_", " ").toUpperCase()}
          </span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${
          log.resource
        }</td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${
          log.ip
        }</td>
      </tr>
    `
      )
      .join("");
  }

  function getActionBadgeClass(action) {
    switch (action) {
      case "folder_access":
        return "permission-read";
      case "permission_change":
        return "permission-write";
      case "user_create":
        return "permission-admin";
      case "file_upload":
        return "permission-read";
      default:
        return "permission-none";
    }
  }

  function createSystemSettingsSection() {
    const main = document.querySelector("main");
    const settingsSection = document.createElement("div");
    settingsSection.id = "settings-section";
    settingsSection.className = "admin-section hidden";
    settingsSection.innerHTML = `
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="admin-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">System Configuration</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Max File Size (MB)</label>
              <input type="number" value="100" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Allowed File Types</label>
              <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md" rows="3">pdf,doc,docx,xls,xlsx,ppt,pptx,jpg,jpeg,png,gif,mp4,avi,zip,rar</textarea>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
              <input type="number" value="30" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
          </div>
        </div>

        <div class="admin-card p-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">Security Settings</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Password Min Length</label>
              <input type="number" value="8" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
              <input type="number" value="5" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Account Lockout Duration (minutes)</label>
              <input type="number" value="15" class="w-full px-3 py-2 border border-gray-300 rounded-md">
            </div>
          </div>
        </div>
      </div>

      <div class="admin-card p-6 mt-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">System Actions</h3>
        <div class="flex space-x-4">
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-save mr-2"></i>
            Save All Settings
          </button>
          <button class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-sync mr-2"></i>
            Restart System
          </button>
          <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
            <i class="fas fa-database mr-2"></i>
            Clear Cache
          </button>
        </div>
      </div>
    `;
    main.appendChild(settingsSection);
  }

  // Initialize the admin panel
  initializeAdminPanel();
});
