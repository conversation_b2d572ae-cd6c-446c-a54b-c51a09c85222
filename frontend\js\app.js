document.addEventListener('DOMContentLoaded', () => {
    const folderTree = document.getElementById('folder-tree');
    const documentList = document.getElementById('document-list');
    const logoutButton = document.getElementById('logout-button');
    const passwordModal = document.getElementById('password-modal');
    const shareModal = document.getElementById('share-modal');

    // Dummy data for initial display
    const dummyFolders = [
        { id: 1, name: 'Folder 1', children: [{ id: 3, name: 'Subfolder 1', children: [] }] },
        { id: 2, name: 'Folder 2', children: [] },
    ];

    const dummyDocuments = [
        { id: 1, name: 'Document 1.pdf' },
        { id: 2, name: 'Document 2.docx' },
    ];

    function renderFolderTree(folders) {
        folderTree.innerHTML = folders.map(folder => `
            <div class="p-2 hover:bg-gray-700 rounded">
                <a href="#" data-folder-id="${folder.id}">${folder.name}</a>
                <div class="pl-4">
                    ${folder.children.map(child => `<div class="p-2 hover:bg-gray-700 rounded"><a href="#" data-folder-id="${child.id}">${child.name}</a></div>`).join('')}
                </div>
            </div>
        `).join('');
    }

    function renderBreadcrumbs(path) {
        const breadcrumbNav = document.getElementById('breadcrumb-nav');
        breadcrumbNav.innerHTML = path.map((item, index) => `
            <div class="flex items-center">
                <a href="#" class="text-gray-500 hover:text-gray-700">${item.name}</a>
                ${index < path.length - 1 ? `<svg class="h-5 w-5 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>` : ''}
            </div>
        `).join('');
    }

    function renderDocumentList(items) {
        documentList.innerHTML = items.map(item => `
            <div class="group relative bg-white p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 flex flex-col items-center text-center">
                <div class="absolute top-2 right-2 z-10">
                    <button class="p-1 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg class="w-5 h-5 text-gray-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" /></svg>
                    </button>
                </div>
                ${item.children ? 
                    `<svg class="w-16 h-16 text-yellow-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" /></svg>` : 
                    `<svg class="w-16 h-16 text-gray-500 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>`
                }
                <span class="text-sm font-medium text-gray-800 truncate w-full">${item.name}</span>
                <span class="text-xs text-gray-500 mt-1">${item.children ? `${item.children.length} items` : 'File'}</span>
            </div>
        `).join('');
    }

    if (logoutButton) {
        logoutButton.addEventListener('click', () => {
            // Placeholder for logout logic
            window.location.href = 'pages/login.html';
        });
    }

    // Initial render
    if (folderTree && documentList) {
        const currentPath = [{ name: 'Home' }];
        renderFolderTree(dummyFolders);
        renderDocumentList([...dummyFolders, ...dummyDocuments]);
        renderBreadcrumbs(currentPath);
    }
});
