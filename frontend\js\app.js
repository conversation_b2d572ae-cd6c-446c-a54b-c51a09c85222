document.addEventListener("DOMContentLoaded", () => {
  const folderTree = document.getElementById("folder-tree");
  const documentList = document.getElementById("document-list");
  const logoutButton = document.getElementById("logout-button");
  const passwordModal = document.getElementById("password-modal");
  const shareModal = document.getElementById("share-modal");
  const contextMenu = document.getElementById("context-menu");
  const gridViewBtn = document.getElementById("grid-view-btn");
  const listViewBtn = document.getElementById("list-view-btn");

  let currentView = "grid";
  let currentPath = [{ id: "root", name: "Home" }];
  let selectedItem = null;

  // Enhanced dummy data with more file types and proper structure
  const fileSystem = {
    root: {
      id: "root",
      name: "Home",
      type: "folder",
      children: [
        {
          id: "documents",
          name: "Documents",
          type: "folder",
          children: [
            {
              id: "doc1",
              name: "Project Report.pdf",
              type: "file",
              size: "2.5 MB",
              modified: "2024-01-15",
            },
            {
              id: "doc2",
              name: "Meeting Notes.docx",
              type: "file",
              size: "1.2 MB",
              modified: "2024-01-14",
            },
            {
              id: "doc3",
              name: "Presentation.pptx",
              type: "file",
              size: "5.8 MB",
              modified: "2024-01-13",
            },
            {
              id: "doc4",
              name: "Spreadsheet.xlsx",
              type: "file",
              size: "890 KB",
              modified: "2024-01-12",
            },
          ],
        },
        {
          id: "images",
          name: "Images",
          type: "folder",
          children: [
            {
              id: "img1",
              name: "photo1.jpg",
              type: "file",
              size: "3.2 MB",
              modified: "2024-01-10",
            },
            {
              id: "img2",
              name: "screenshot.png",
              type: "file",
              size: "1.5 MB",
              modified: "2024-01-09",
            },
            {
              id: "img3",
              name: "logo.svg",
              type: "file",
              size: "45 KB",
              modified: "2024-01-08",
            },
          ],
        },
        {
          id: "videos",
          name: "Videos",
          type: "folder",
          children: [
            {
              id: "vid1",
              name: "tutorial.mp4",
              type: "file",
              size: "125 MB",
              modified: "2024-01-07",
            },
            {
              id: "vid2",
              name: "demo.avi",
              type: "file",
              size: "89 MB",
              modified: "2024-01-06",
            },
          ],
        },
        {
          id: "archive",
          name: "Archive",
          type: "folder",
          children: [
            {
              id: "zip1",
              name: "backup.zip",
              type: "file",
              size: "15 MB",
              modified: "2024-01-05",
            },
            {
              id: "zip2",
              name: "old_files.rar",
              type: "file",
              size: "8.5 MB",
              modified: "2024-01-04",
            },
          ],
        },
      ],
    },
  };

  // Get file icon based on file extension
  function getFileIcon(fileName, type) {
    if (type === "folder") {
      return "fas fa-folder text-blue-500";
    }

    const extension = fileName.split(".").pop().toLowerCase();
    const iconMap = {
      pdf: "fas fa-file-pdf text-red-500",
      doc: "fas fa-file-word text-blue-600",
      docx: "fas fa-file-word text-blue-600",
      xls: "fas fa-file-excel text-green-600",
      xlsx: "fas fa-file-excel text-green-600",
      ppt: "fas fa-file-powerpoint text-orange-500",
      pptx: "fas fa-file-powerpoint text-orange-500",
      jpg: "fas fa-file-image text-purple-500",
      jpeg: "fas fa-file-image text-purple-500",
      png: "fas fa-file-image text-purple-500",
      gif: "fas fa-file-image text-purple-500",
      svg: "fas fa-file-image text-purple-500",
      mp4: "fas fa-file-video text-red-600",
      avi: "fas fa-file-video text-red-600",
      mov: "fas fa-file-video text-red-600",
      mp3: "fas fa-file-audio text-green-500",
      wav: "fas fa-file-audio text-green-500",
      zip: "fas fa-file-archive text-yellow-600",
      rar: "fas fa-file-archive text-yellow-600",
      "7z": "fas fa-file-archive text-yellow-600",
      txt: "fas fa-file-alt text-gray-600",
      js: "fas fa-file-code text-yellow-500",
      html: "fas fa-file-code text-orange-600",
      css: "fas fa-file-code text-blue-500",
      json: "fas fa-file-code text-green-500",
    };

    return iconMap[extension] || "fas fa-file text-gray-500";
  }

  // Get current folder contents
  function getCurrentFolderContents() {
    let current = fileSystem.root;
    for (let i = 1; i < currentPath.length; i++) {
      current = current.children.find((item) => item.id === currentPath[i].id);
    }
    return current ? current.children || [] : [];
  }

  // Render folder tree in sidebar
  function renderFolderTree(folder = fileSystem.root, level = 0) {
    if (!folder.children) return "";

    return folder.children
      .filter((item) => item.type === "folder")
      .map(
        (item) => `
        <div class="folder-tree-item">
          <div class="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors"
               data-folder-id="${item.id}" onclick="navigateToFolder('${
          item.id
        }')">
            <i class="fas fa-folder text-blue-400 mr-2"></i>
            <span class="text-sm">${item.name}</span>
            <span class="ml-auto text-xs text-gray-400">${
              item.children ? item.children.length : 0
            }</span>
          </div>
          ${level < 2 ? renderFolderTree(item, level + 1) : ""}
        </div>
      `
      )
      .join("");
  }

  function renderBreadcrumbs(path) {
    const breadcrumbNav = document.getElementById("breadcrumb-nav");
    breadcrumbNav.innerHTML = `
      <i class="fas fa-home text-gray-500 mr-2"></i>
      ${path
        .map(
          (item, index) => `
        <div class="breadcrumb-item">
          <button class="text-gray-600 hover:text-gray-800 font-medium" onclick="navigateToPath(${index})">
            ${item.name}
          </button>
          ${
            index < path.length - 1
              ? '<i class="fas fa-chevron-right text-gray-400 mx-2"></i>'
              : ""
          }
        </div>
      `
        )
        .join("")}
    `;
  }

  function renderDocumentList(items) {
    if (!items || items.length === 0) {
      documentList.innerHTML = `
        <div class="col-span-full flex flex-col items-center justify-center py-12 text-gray-500">
          <i class="fas fa-folder-open text-6xl mb-4"></i>
          <p class="text-lg font-medium">This folder is empty</p>
          <p class="text-sm">Upload files or create folders to get started</p>
        </div>
      `;
      return;
    }

    documentList.innerHTML = items
      .map(
        (item) => `
        <div class="file-item group relative bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-100"
             data-item-id="${item.id}"
             onclick="handleItemClick('${item.id}', '${item.type}')"
             oncontextmenu="showContextMenu(event, '${item.id}')">

          <!-- Options Menu -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button class="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700"
                    onclick="event.stopPropagation(); showContextMenu(event, '${
                      item.id
                    }')">
              <i class="fas fa-ellipsis-v text-sm"></i>
            </button>
          </div>

          <!-- File/Folder Icon -->
          <div class="flex flex-col items-center text-center">
            <div class="mb-3">
              <i class="${getFileIcon(item.name, item.type)} text-4xl"></i>
            </div>

            <!-- Name -->
            <h3 class="text-sm font-medium text-gray-900 truncate w-full mb-1" title="${
              item.name
            }">
              ${item.name}
            </h3>

            <!-- Details -->
            <div class="text-xs text-gray-500 space-y-1">
              ${
                item.type === "folder"
                  ? `<div><i class="fas fa-folder mr-1"></i>${
                      item.children ? item.children.length : 0
                    } items</div>`
                  : `<div><i class="fas fa-file mr-1"></i>${
                      item.size || "Unknown size"
                    }</div>`
              }
              ${
                item.modified
                  ? `<div><i class="fas fa-clock mr-1"></i>${item.modified}</div>`
                  : ""
              }
            </div>
          </div>
        </div>
      `
      )
      .join("");
  }

  // Navigation functions
  window.navigateToFolder = function (folderId) {
    const folder = findItemById(folderId);
    if (folder && folder.type === "folder") {
      currentPath.push({ id: folderId, name: folder.name });
      updateView();
    }
  };

  window.navigateToPath = function (pathIndex) {
    currentPath = currentPath.slice(0, pathIndex + 1);
    updateView();
  };

  window.handleItemClick = function (itemId, itemType) {
    if (itemType === "folder") {
      navigateToFolder(itemId);
    } else {
      // Handle file click (preview, download, etc.)
      console.log("File clicked:", itemId);
    }
  };

  window.showContextMenu = function (event, itemId) {
    event.preventDefault();
    selectedItem = itemId;

    contextMenu.style.display = "block";
    contextMenu.style.left = event.pageX + "px";
    contextMenu.style.top = event.pageY + "px";
  };

  // Helper function to find item by ID
  function findItemById(id, items = fileSystem.root.children) {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = findItemById(id, item.children);
        if (found) return found;
      }
    }
    return null;
  }

  // Update the entire view
  function updateView() {
    const contents = getCurrentFolderContents();
    renderDocumentList(contents);
    renderBreadcrumbs(currentPath);
    folderTree.innerHTML = renderFolderTree();
  }

  // View toggle functionality
  gridViewBtn.addEventListener("click", () => {
    currentView = "grid";
    gridViewBtn.classList.add("bg-white", "shadow-sm", "text-gray-700");
    gridViewBtn.classList.remove("text-gray-500");
    listViewBtn.classList.remove("bg-white", "shadow-sm", "text-gray-700");
    listViewBtn.classList.add("text-gray-500");
    documentList.className = "file-grid";
  });

  listViewBtn.addEventListener("click", () => {
    currentView = "list";
    listViewBtn.classList.add("bg-white", "shadow-sm", "text-gray-700");
    listViewBtn.classList.remove("text-gray-500");
    gridViewBtn.classList.remove("bg-white", "shadow-sm", "text-gray-700");
    gridViewBtn.classList.add("text-gray-500");
    documentList.className = "space-y-2";
  });

  // Context menu functionality
  document.addEventListener("click", (e) => {
    if (!contextMenu.contains(e.target)) {
      contextMenu.style.display = "none";
    }
  });

  contextMenu.addEventListener("click", (e) => {
    const action = e.target.closest(".context-menu-item")?.dataset.action;
    if (action && selectedItem) {
      handleContextAction(action, selectedItem);
      contextMenu.style.display = "none";
    }
  });

  function handleContextAction(action, itemId) {
    const item = findItemById(itemId);
    if (!item) return;

    switch (action) {
      case "open":
        if (item.type === "folder") {
          navigateToFolder(itemId);
        }
        break;
      case "rename":
        const newName = prompt("Enter new name:", item.name);
        if (newName && newName !== item.name) {
          item.name = newName;
          updateView();
        }
        break;
      case "delete":
        if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
          // Remove item from parent
          console.log("Delete item:", itemId);
          updateView();
        }
        break;
      case "share":
        // Show share modal
        document.getElementById(
          "modal-share-link"
        ).value = `${window.location.origin}/share/${itemId}`;
        shareModal.classList.remove("hidden");
        break;
      case "download":
        console.log("Download item:", itemId);
        break;
    }
  }

  // Logout functionality
  if (logoutButton) {
    logoutButton.addEventListener("click", () => {
      window.location.href = "pages/login.html";
    });
  }

  // Initial render
  if (folderTree && documentList) {
    updateView();
  }
});
