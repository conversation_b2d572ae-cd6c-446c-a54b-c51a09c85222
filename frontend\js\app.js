document.addEventListener("DOMContentLoaded", () => {
  const folderTree = document.getElementById("folder-tree");
  const documentList = document.getElementById("document-list");
  const logoutButton = document.getElementById("logout-button");
  const passwordModal = document.getElementById("password-modal");
  const shareModal = document.getElementById("share-modal");
  const contextMenu = document.getElementById("context-menu");
  const gridViewBtn = document.getElementById("grid-view-btn");
  const listViewBtn = document.getElementById("list-view-btn");

  let currentView = "grid";
  let currentPath = [{ id: "root", name: "Home" }];
  let selectedItem = null;

  // Enhanced dummy data with more file types and proper structure
  const fileSystem = {
    root: {
      id: "root",
      name: "Home",
      type: "folder",
      children: [
        {
          id: "documents",
          name: "Documents",
          type: "folder",
          children: [
            {
              id: "doc1",
              name: "Project Report.pdf",
              type: "file",
              size: "2.5 MB",
              modified: "2024-01-15",
            },
            {
              id: "doc2",
              name: "Meeting Notes.docx",
              type: "file",
              size: "1.2 MB",
              modified: "2024-01-14",
            },
            {
              id: "doc3",
              name: "Presentation.pptx",
              type: "file",
              size: "5.8 MB",
              modified: "2024-01-13",
            },
            {
              id: "doc4",
              name: "Spreadsheet.xlsx",
              type: "file",
              size: "890 KB",
              modified: "2024-01-12",
            },
          ],
        },
        {
          id: "images",
          name: "Images",
          type: "folder",
          children: [
            {
              id: "img1",
              name: "photo1.jpg",
              type: "file",
              size: "3.2 MB",
              modified: "2024-01-10",
            },
            {
              id: "img2",
              name: "screenshot.png",
              type: "file",
              size: "1.5 MB",
              modified: "2024-01-09",
            },
            {
              id: "img3",
              name: "logo.svg",
              type: "file",
              size: "45 KB",
              modified: "2024-01-08",
            },
          ],
        },
        {
          id: "videos",
          name: "Videos",
          type: "folder",
          children: [
            {
              id: "vid1",
              name: "tutorial.mp4",
              type: "file",
              size: "125 MB",
              modified: "2024-01-07",
            },
            {
              id: "vid2",
              name: "demo.avi",
              type: "file",
              size: "89 MB",
              modified: "2024-01-06",
            },
          ],
        },
        {
          id: "archive",
          name: "Archive",
          type: "folder",
          children: [
            {
              id: "zip1",
              name: "backup.zip",
              type: "file",
              size: "15 MB",
              modified: "2024-01-05",
            },
            {
              id: "zip2",
              name: "old_files.rar",
              type: "file",
              size: "8.5 MB",
              modified: "2024-01-04",
            },
          ],
        },
      ],
    },
  };

  // Get file icon based on file extension
  function getFileIcon(fileName, type) {
    if (type === "folder") {
      return "fas fa-folder text-blue-500";
    }

    const extension = fileName.split(".").pop().toLowerCase();
    const iconMap = {
      pdf: "fas fa-file-pdf text-red-500",
      doc: "fas fa-file-word text-blue-600",
      docx: "fas fa-file-word text-blue-600",
      xls: "fas fa-file-excel text-green-600",
      xlsx: "fas fa-file-excel text-green-600",
      ppt: "fas fa-file-powerpoint text-orange-500",
      pptx: "fas fa-file-powerpoint text-orange-500",
      jpg: "fas fa-file-image text-purple-500",
      jpeg: "fas fa-file-image text-purple-500",
      png: "fas fa-file-image text-purple-500",
      gif: "fas fa-file-image text-purple-500",
      svg: "fas fa-file-image text-purple-500",
      mp4: "fas fa-file-video text-red-600",
      avi: "fas fa-file-video text-red-600",
      mov: "fas fa-file-video text-red-600",
      mp3: "fas fa-file-audio text-green-500",
      wav: "fas fa-file-audio text-green-500",
      zip: "fas fa-file-archive text-yellow-600",
      rar: "fas fa-file-archive text-yellow-600",
      "7z": "fas fa-file-archive text-yellow-600",
      txt: "fas fa-file-alt text-gray-600",
      js: "fas fa-file-code text-yellow-500",
      html: "fas fa-file-code text-orange-600",
      css: "fas fa-file-code text-blue-500",
      json: "fas fa-file-code text-green-500",
    };

    return iconMap[extension] || "fas fa-file text-gray-500";
  }

  // Get current folder contents
  function getCurrentFolderContents() {
    let current = fileSystem.root;
    for (let i = 1; i < currentPath.length; i++) {
      current = current.children.find((item) => item.id === currentPath[i].id);
    }
    return current ? current.children || [] : [];
  }

  // Render folder tree in sidebar
  function renderFolderTree(folder = fileSystem.root, level = 0) {
    if (!folder.children) return "";

    return folder.children
      .filter((item) => item.type === "folder")
      .map(
        (item) => `
        <div class="folder-tree-item">
          <div class="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-700 transition-colors"
               data-folder-id="${item.id}" onclick="navigateToFolder('${
          item.id
        }')">
            <i class="fas fa-folder text-blue-400 mr-2"></i>
            <span class="text-sm">${item.name}</span>
            <span class="ml-auto text-xs text-gray-400">${
              item.children ? item.children.length : 0
            }</span>
          </div>
          ${level < 2 ? renderFolderTree(item, level + 1) : ""}
        </div>
      `
      )
      .join("");
  }

  function renderBreadcrumbs(path) {
    const breadcrumbNav = document.getElementById("breadcrumb-nav");
    breadcrumbNav.innerHTML = `
      <i class="fas fa-home text-gray-500 mr-2"></i>
      ${path
        .map(
          (item, index) => `
        <div class="breadcrumb-item">
          <button class="text-gray-600 hover:text-gray-800 font-medium" onclick="navigateToPath(${index})">
            ${item.name}
          </button>
          ${
            index < path.length - 1
              ? '<i class="fas fa-chevron-right text-gray-400 mx-2"></i>'
              : ""
          }
        </div>
      `
        )
        .join("")}
    `;
  }

  function renderDocumentList(items) {
    if (!items || items.length === 0) {
      documentList.innerHTML = `
        <div class="col-span-full flex flex-col items-center justify-center py-12 text-gray-500">
          <i class="fas fa-folder-open text-6xl mb-4"></i>
          <p class="text-lg font-medium">This folder is empty</p>
          <p class="text-sm">Upload files or create folders to get started</p>
        </div>
      `;
      return;
    }

    documentList.innerHTML = items
      .map(
        (item) => `
        <div class="file-item group relative bg-white p-4 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer border border-gray-100 ${
          item.type === "folder" ? "drop-zone" : ""
        }"
             data-item-id="${item.id}"
             data-item-type="${item.type}"
             data-item-name="${item.name}"
             draggable="true"
             onclick="handleItemClick('${item.id}', '${item.type}')"
             ondblclick="handleItemDoubleClick('${item.id}', '${item.type}')"
             oncontextmenu="showContextMenu(event, '${item.id}')"
             ondragstart="handleDragStart(event, '${item.id}')"
             ondragover="handleDragOver(event)"
             ondragenter="handleDragEnter(event)"
             ondragleave="handleDragLeave(event)"
             ondrop="handleDrop(event, '${item.id}')"
             ondragend="handleDragEnd(event)">

          <!-- Options Menu -->
          <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <button class="p-1 rounded-full hover:bg-gray-100 text-gray-500 hover:text-gray-700"
                    onclick="event.stopPropagation(); showContextMenu(event, '${
                      item.id
                    }')">
              <i class="fas fa-ellipsis-v text-sm"></i>
            </button>
          </div>

          <!-- File/Folder Icon -->
          <div class="flex flex-col items-center text-center">
            <div class="mb-3">
              <i class="folder-icon ${getFileIcon(
                item.name,
                item.type
              )} text-4xl"></i>
            </div>

            <!-- Name -->
            <h3 class="text-sm font-medium text-gray-900 truncate w-full mb-1" title="${
              item.name
            }">
              ${item.name}
            </h3>

            <!-- Details -->
            <div class="text-xs text-gray-500 space-y-1">
              ${
                item.type === "folder"
                  ? `<div><i class="fas fa-folder mr-1"></i>${
                      item.children ? item.children.length : 0
                    } items</div>`
                  : `<div><i class="fas fa-file mr-1"></i>${
                      item.size || "Unknown size"
                    }</div>`
              }
              ${
                item.modified
                  ? `<div><i class="fas fa-clock mr-1"></i>${item.modified}</div>`
                  : ""
              }
            </div>
          </div>
        </div>
      `
      )
      .join("");
  }

  // Navigation functions
  window.navigateToFolder = function (folderId) {
    const folder = findItemById(folderId);
    if (folder && folder.type === "folder") {
      currentPath.push({ id: folderId, name: folder.name });
      updateView();
    }
  };

  window.navigateToPath = function (pathIndex) {
    currentPath = currentPath.slice(0, pathIndex + 1);
    updateView();
  };

  window.handleItemClick = function (itemId, itemType) {
    // Single click just selects the item
    selectedItem = itemId;

    // Remove previous selection highlights
    document.querySelectorAll(".file-item").forEach((item) => {
      item.classList.remove("ring-2", "ring-blue-500");
    });

    // Highlight selected item
    const itemElement = document.querySelector(`[data-item-id="${itemId}"]`);
    if (itemElement) {
      itemElement.classList.add("ring-2", "ring-blue-500");
    }
  };

  window.handleItemDoubleClick = function (itemId, itemType) {
    if (itemType === "folder") {
      navigateToFolder(itemId);
    } else {
      // Handle file double-click (open/preview)
      console.log("File double-clicked:", itemId);
    }
  };

  window.showContextMenu = function (event, itemId) {
    event.preventDefault();
    selectedItem = itemId;

    contextMenu.style.display = "block";
    contextMenu.style.left = event.pageX + "px";
    contextMenu.style.top = event.pageY + "px";
  };

  // Helper function to find item by ID
  function findItemById(id, items = fileSystem.root.children) {
    for (const item of items) {
      if (item.id === id) return item;
      if (item.children) {
        const found = findItemById(id, item.children);
        if (found) return found;
      }
    }
    return null;
  }

  // Update the entire view
  function updateView() {
    const contents = getCurrentFolderContents();
    renderDocumentList(contents);
    renderBreadcrumbs(currentPath);
    folderTree.innerHTML = renderFolderTree();
  }

  // View toggle functionality
  gridViewBtn.addEventListener("click", () => {
    currentView = "grid";
    gridViewBtn.classList.add("bg-white", "shadow-sm", "text-gray-700");
    gridViewBtn.classList.remove("text-gray-500");
    listViewBtn.classList.remove("bg-white", "shadow-sm", "text-gray-700");
    listViewBtn.classList.add("text-gray-500");
    documentList.className = "file-grid";
  });

  listViewBtn.addEventListener("click", () => {
    currentView = "list";
    listViewBtn.classList.add("bg-white", "shadow-sm", "text-gray-700");
    listViewBtn.classList.remove("text-gray-500");
    gridViewBtn.classList.remove("bg-white", "shadow-sm", "text-gray-700");
    gridViewBtn.classList.add("text-gray-500");
    documentList.className = "space-y-2";
  });

  // Context menu functionality
  document.addEventListener("click", (e) => {
    if (!contextMenu.contains(e.target)) {
      contextMenu.style.display = "none";
    }
  });

  contextMenu.addEventListener("click", (e) => {
    const action = e.target.closest(".context-menu-item")?.dataset.action;
    if (action && selectedItem) {
      handleContextAction(action, selectedItem);
      contextMenu.style.display = "none";
    }
  });

  function handleContextAction(action, itemId) {
    const item = findItemById(itemId);
    if (!item) return;

    switch (action) {
      case "open":
        if (item.type === "folder") {
          navigateToFolder(itemId);
        }
        break;
      case "rename":
        const newName = prompt("Enter new name:", item.name);
        if (newName && newName !== item.name) {
          item.name = newName;
          updateView();
        }
        break;
      case "move":
        showMoveModal(itemId);
        break;
      case "delete":
        if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
          removeItemFromParent(itemId);
          updateView();
        }
        break;
      case "share":
        // Show share modal
        document.getElementById(
          "modal-share-link"
        ).value = `${window.location.origin}/share/${itemId}`;
        shareModal.classList.remove("hidden");
        break;
      case "download":
        console.log("Download item:", itemId);
        break;
      case "properties":
        showPropertiesModal(itemId);
        break;
    }
  }

  // Logout functionality
  if (logoutButton) {
    logoutButton.addEventListener("click", () => {
      window.location.href = "pages/login.html";
    });
  }

  // Drag and Drop functionality
  let draggedItem = null;

  window.handleDragStart = function (event, itemId) {
    draggedItem = itemId;
    event.target.classList.add("dragging");
    event.dataTransfer.effectAllowed = "move";
    event.dataTransfer.setData("text/html", event.target.outerHTML);
  };

  window.handleDragEnd = function (event) {
    event.target.classList.remove("dragging");
    // Remove drag-over class from all elements
    document.querySelectorAll(".drag-over").forEach((el) => {
      el.classList.remove("drag-over");
      // Reset folder icon to normal
      const folderIcon = el.querySelector(".folder-icon");
      if (folderIcon && folderIcon.classList.contains("fas", "fa-folder")) {
        folderIcon.classList.remove("fa-folder-open");
        folderIcon.classList.add("fa-folder");
      }
    });
    draggedItem = null;
  };

  window.handleDragOver = function (event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  };

  window.handleDragEnter = function (event) {
    event.preventDefault();
    const target = event.currentTarget;
    const targetType = target.dataset.itemType;

    // Only allow dropping on folders and prevent dropping on self
    if (targetType === "folder" && target.dataset.itemId !== draggedItem) {
      target.classList.add("drag-over");

      // Change folder icon to open folder
      const folderIcon = target.querySelector(".folder-icon");
      if (folderIcon && folderIcon.classList.contains("fas", "fa-folder")) {
        folderIcon.classList.remove("fa-folder");
        folderIcon.classList.add("fa-folder-open");
      }
    }
  };

  window.handleDragLeave = function (event) {
    const target = event.currentTarget;
    const rect = target.getBoundingClientRect();
    const x = event.clientX;
    const y = event.clientY;

    // Only remove drag-over if mouse is actually outside the element
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      target.classList.remove("drag-over");

      // Reset folder icon to normal
      const folderIcon = target.querySelector(".folder-icon");
      if (
        folderIcon &&
        folderIcon.classList.contains("fas", "fa-folder-open")
      ) {
        folderIcon.classList.remove("fa-folder-open");
        folderIcon.classList.add("fa-folder");
      }
    }
  };

  window.handleDrop = function (event, targetFolderId) {
    event.preventDefault();
    const target = event.currentTarget;
    target.classList.remove("drag-over");

    // Reset folder icon to normal
    const folderIcon = target.querySelector(".folder-icon");
    if (folderIcon && folderIcon.classList.contains("fas", "fa-folder-open")) {
      folderIcon.classList.remove("fa-folder-open");
      folderIcon.classList.add("fa-folder");
    }

    // Don't allow dropping on self or non-folders
    if (
      !draggedItem ||
      draggedItem === targetFolderId ||
      target.dataset.itemType !== "folder"
    ) {
      return;
    }

    // Move the item
    moveItem(draggedItem, targetFolderId);
  };

  function moveItem(itemId, targetFolderId) {
    const item = findItemById(itemId);
    const targetFolder = findItemById(targetFolderId);

    if (!item || !targetFolder || targetFolder.type !== "folder") {
      return;
    }

    // Remove item from its current parent
    removeItemFromParent(itemId);

    // Add item to target folder
    if (!targetFolder.children) {
      targetFolder.children = [];
    }
    targetFolder.children.push(item);

    // Update the view
    updateView();

    // Show success message
    console.log(`Moved "${item.name}" to "${targetFolder.name}"`);
  }

  function removeItemFromParent(itemId) {
    function removeFromChildren(children) {
      if (!children) return false;

      for (let i = 0; i < children.length; i++) {
        if (children[i].id === itemId) {
          children.splice(i, 1);
          return true;
        }
        if (children[i].children && removeFromChildren(children[i].children)) {
          return true;
        }
      }
      return false;
    }

    removeFromChildren(fileSystem.root.children);
  }

  // Properties Modal Functions
  function showPropertiesModal(itemId) {
    const item = findItemById(itemId);
    if (!item) return;

    const propertiesModal = document.getElementById("properties-modal");
    const propertiesContent = document.getElementById("properties-content");

    const createdDate = item.created || "Unknown";
    const modifiedDate = item.modified || "Unknown";
    const itemSize =
      item.size ||
      (item.type === "folder"
        ? `${item.children ? item.children.length : 0} items`
        : "Unknown");

    propertiesContent.innerHTML = `
      <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
        <i class="${getFileIcon(item.name, item.type)} text-3xl"></i>
        <div>
          <h4 class="font-medium text-gray-900">${item.name}</h4>
          <p class="text-sm text-gray-500">${
            item.type === "folder" ? "Folder" : "File"
          }</p>
        </div>
      </div>
      <div class="space-y-2">
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-700">Type:</span>
          <span class="text-sm text-gray-900">${
            item.type === "folder" ? "Folder" : getFileType(item.name)
          }</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-700">Size:</span>
          <span class="text-sm text-gray-900">${itemSize}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-700">Modified:</span>
          <span class="text-sm text-gray-900">${modifiedDate}</span>
        </div>
        <div class="flex justify-between">
          <span class="text-sm font-medium text-gray-700">Created:</span>
          <span class="text-sm text-gray-900">${createdDate}</span>
        </div>
      </div>
    `;

    propertiesModal.classList.remove("hidden");
  }

  function getFileType(fileName) {
    const extension = fileName.split(".").pop().toLowerCase();
    const typeMap = {
      pdf: "PDF Document",
      doc: "Word Document",
      docx: "Word Document",
      xls: "Excel Spreadsheet",
      xlsx: "Excel Spreadsheet",
      ppt: "PowerPoint Presentation",
      pptx: "PowerPoint Presentation",
      jpg: "JPEG Image",
      jpeg: "JPEG Image",
      png: "PNG Image",
      gif: "GIF Image",
      svg: "SVG Image",
      mp4: "MP4 Video",
      avi: "AVI Video",
      mov: "MOV Video",
      mp3: "MP3 Audio",
      wav: "WAV Audio",
      zip: "ZIP Archive",
      rar: "RAR Archive",
      "7z": "7-Zip Archive",
      txt: "Text Document",
      js: "JavaScript File",
      html: "HTML Document",
      css: "CSS Stylesheet",
      json: "JSON File",
    };
    return typeMap[extension] || "Unknown File Type";
  }

  // Move Modal Functions
  let selectedMoveTarget = null;

  function showMoveModal(itemId) {
    const item = findItemById(itemId);
    if (!item) return;

    const moveModal = document.getElementById("move-modal");
    const folderTreeModal = document.getElementById("folder-tree-modal");

    // Generate folder tree for selection
    folderTreeModal.innerHTML = renderFolderTreeForMove(
      fileSystem.root,
      itemId
    );

    moveModal.classList.remove("hidden");
    selectedMoveTarget = null;
  }

  function renderFolderTreeForMove(folder, excludeId, level = 0) {
    if (!folder.children) return "";

    return folder.children
      .filter((item) => item.type === "folder" && item.id !== excludeId)
      .map(
        (item) => `
        <div class="folder-tree-move-item" style="margin-left: ${level * 20}px">
          <div class="flex items-center p-2 rounded cursor-pointer hover:bg-blue-100 ${
            selectedMoveTarget === item.id ? "bg-blue-200" : ""
          }"
               onclick="selectMoveTarget('${item.id}')">
            <i class="fas fa-folder text-blue-500 mr-2"></i>
            <span class="text-sm">${item.name}</span>
          </div>
          ${renderFolderTreeForMove(item, excludeId, level + 1)}
        </div>
      `
      )
      .join("");
  }

  window.selectMoveTarget = function (folderId) {
    selectedMoveTarget = folderId;

    // Update visual selection
    document.querySelectorAll(".folder-tree-move-item > div").forEach((el) => {
      el.classList.remove("bg-blue-200");
    });

    const targetElement = document.querySelector(
      `[onclick="selectMoveTarget('${folderId}')"]`
    );
    if (targetElement) {
      targetElement.classList.add("bg-blue-200");
    }
  };

  // Modal Event Listeners
  const propertiesModal = document.getElementById("properties-modal");
  const moveModal = document.getElementById("move-modal");

  // Properties Modal
  document
    .getElementById("close-properties-modal")
    .addEventListener("click", () => {
      propertiesModal.classList.add("hidden");
    });

  document.getElementById("properties-ok-btn").addEventListener("click", () => {
    propertiesModal.classList.add("hidden");
  });

  // Move Modal
  document.getElementById("close-move-modal").addEventListener("click", () => {
    moveModal.classList.add("hidden");
  });

  document.getElementById("move-cancel-btn").addEventListener("click", () => {
    moveModal.classList.add("hidden");
  });

  document.getElementById("move-confirm-btn").addEventListener("click", () => {
    if (selectedItem && selectedMoveTarget) {
      moveItem(selectedItem, selectedMoveTarget);
      moveModal.classList.add("hidden");
    } else {
      alert("Please select a destination folder.");
    }
  });

  // Close modals when clicking outside
  window.addEventListener("click", (e) => {
    if (e.target === propertiesModal) {
      propertiesModal.classList.add("hidden");
    }
    if (e.target === moveModal) {
      moveModal.classList.add("hidden");
    }
  });

  // Upload and New Folder Functionality
  const createFolderBtn = document.getElementById("create-folder-button");
  const uploadFileBtn = document.getElementById("upload-file-button");
  const fileInput = document.getElementById("file-input");
  const uploadDropZone = document.getElementById("upload-drop-zone");

  // Create New Folder
  if (createFolderBtn) {
    createFolderBtn.addEventListener("click", () => {
      const folderName = prompt("Enter folder name:");
      if (folderName && folderName.trim()) {
        createNewFolder(folderName.trim());
      }
    });
  }

  // Upload Files Button
  if (uploadFileBtn && fileInput) {
    uploadFileBtn.addEventListener("click", () => {
      fileInput.click();
    });

    fileInput.addEventListener("change", (e) => {
      handleFileUpload(e.target.files);
    });
  }

  // Upload Drop Zone
  if (uploadDropZone) {
    uploadDropZone.addEventListener("click", () => {
      fileInput.click();
    });
  }

  // Global Drag and Drop for Moving Files/Folders
  const mainContent = document.querySelector("main");
  let isMovingItem = false;

  // Add global drag and drop to main content area (no visual feedback for moving items)
  if (mainContent) {
    mainContent.addEventListener("dragover", (e) => {
      e.preventDefault();
      if (draggedItem) {
        e.dataTransfer.dropEffect = "move";
        // No visual feedback for moving items
      }
    });

    mainContent.addEventListener("dragenter", (e) => {
      e.preventDefault();
      // No visual feedback for moving items
    });

    mainContent.addEventListener("dragleave", (e) => {
      e.preventDefault();
      // No visual feedback for moving items
    });

    mainContent.addEventListener("drop", (e) => {
      e.preventDefault();
      // Immediately hide any upload overlay
      hideUploadDropZone();

      // If dropping and not on a specific folder, move to current folder
      if (draggedItem) {
        const targetFolder = e.target.closest(
          ".file-item[data-item-type='folder']"
        );

        if (targetFolder) {
          // Dropping on a specific folder
          const targetFolderId = targetFolder.dataset.itemId;
          if (targetFolderId && targetFolderId !== draggedItem) {
            moveItem(draggedItem, targetFolderId);
          }
        } else {
          // Dropping on empty space - move to current folder
          const currentFolder = getCurrentFolder();
          if (currentFolder && draggedItem !== currentFolder.id) {
            const draggedItemObj = findItemById(draggedItem);
            const isAlreadyInCurrentFolder =
              currentFolder.children &&
              currentFolder.children.some((child) => child.id === draggedItem);

            if (!isAlreadyInCurrentFolder && draggedItemObj) {
              moveItem(draggedItem, currentFolder.id);
            }
          }
        }
      }
    });
  }

  // Add drag and drop to sidebar folders
  const sidebar = document.querySelector(".w-64");
  if (sidebar) {
    sidebar.addEventListener("dragover", (e) => {
      e.preventDefault();
      if (draggedItem) {
        const folderItem = e.target.closest("[data-folder-id]");
        if (folderItem) {
          e.dataTransfer.dropEffect = "move";
          folderItem.classList.add("sidebar-drop-hover");
        }
      }
    });

    sidebar.addEventListener("dragenter", (e) => {
      e.preventDefault();
      if (draggedItem) {
        const folderItem = e.target.closest("[data-folder-id]");
        if (folderItem) {
          folderItem.classList.add("sidebar-drop-hover");
        }
      }
    });

    sidebar.addEventListener("dragleave", (e) => {
      e.preventDefault();
      const folderItem = e.target.closest("[data-folder-id]");
      if (folderItem && !folderItem.contains(e.relatedTarget)) {
        folderItem.classList.remove("sidebar-drop-hover");
      }
    });

    sidebar.addEventListener("drop", (e) => {
      e.preventDefault();
      const folderItem = e.target.closest("[data-folder-id]");
      if (folderItem && draggedItem) {
        const targetFolderId = folderItem.dataset.folderId;
        if (targetFolderId && targetFolderId !== draggedItem) {
          moveItem(draggedItem, targetFolderId);
        }
        folderItem.classList.remove("sidebar-drop-hover");
      }
    });
  }

  // Drag and Drop Upload Functionality
  let dragCounter = 0;

  // Show drop zone when dragging files over the window (only for external files, not internal items)
  window.addEventListener("dragenter", (e) => {
    e.preventDefault();
    dragCounter++;
    // Only show upload zone for external files, not internal drag operations
    if (e.dataTransfer.types.includes("Files") && !draggedItem) {
      showUploadDropZone();
    }
  });

  window.addEventListener("dragleave", (e) => {
    e.preventDefault();
    dragCounter--;
    if (dragCounter === 0) {
      hideUploadDropZone();
    }
  });

  window.addEventListener("dragover", (e) => {
    e.preventDefault();
    if (e.dataTransfer.types.includes("Files") && !draggedItem) {
      e.dataTransfer.dropEffect = "copy";
    }
  });

  window.addEventListener("drop", (e) => {
    e.preventDefault();
    dragCounter = 0;
    // Immediately hide upload overlay
    hideUploadDropZone();

    // Only handle file uploads if not moving internal items
    if (e.dataTransfer.files.length > 0 && !draggedItem) {
      handleFileUpload(e.dataTransfer.files);
    }
  });

  // Upload drop zone specific events
  if (uploadDropZone) {
    uploadDropZone.addEventListener("dragover", (e) => {
      e.preventDefault();
      e.stopPropagation();
      uploadDropZone.classList.add("drag-over");
    });

    uploadDropZone.addEventListener("dragleave", (e) => {
      e.preventDefault();
      e.stopPropagation();
      uploadDropZone.classList.remove("drag-over");
    });

    uploadDropZone.addEventListener("drop", (e) => {
      e.preventDefault();
      e.stopPropagation();
      uploadDropZone.classList.remove("drag-over");
      // Immediately hide upload overlay
      hideUploadDropZone();
      handleFileUpload(e.dataTransfer.files);
    });
  }

  function createNewFolder(folderName) {
    const currentFolder = getCurrentFolder();
    if (!currentFolder) return;

    // Generate unique ID
    const newFolderId = "folder_" + Date.now();

    // Create new folder object
    const newFolder = {
      id: newFolderId,
      name: folderName,
      type: "folder",
      children: [],
      created: new Date().toLocaleDateString(),
      modified: new Date().toLocaleDateString(),
    };

    // Add to current folder
    if (!currentFolder.children) {
      currentFolder.children = [];
    }
    currentFolder.children.push(newFolder);

    // Update view
    updateView();

    console.log(`Created new folder: ${folderName}`);
  }

  function handleFileUpload(files) {
    const currentFolder = getCurrentFolder();
    if (!currentFolder) return;

    Array.from(files).forEach((file) => {
      // Generate unique ID
      const fileId =
        "file_" + Date.now() + "_" + Math.random().toString(36).substr(2, 9);

      // Create file object
      const newFile = {
        id: fileId,
        name: file.name,
        type: "file",
        size: formatFileSize(file.size),
        created: new Date().toLocaleDateString(),
        modified: new Date().toLocaleDateString(),
      };

      // Add to current folder
      if (!currentFolder.children) {
        currentFolder.children = [];
      }
      currentFolder.children.push(newFile);
    });

    // Update view
    updateView();

    console.log(`Uploaded ${files.length} file(s)`);
  }

  function getCurrentFolder() {
    if (currentPath.length === 1) {
      return fileSystem.root;
    }

    const currentFolderId = currentPath[currentPath.length - 1].id;
    return findItemById(currentFolderId);
  }

  function formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  function showUploadDropZone() {
    if (uploadDropZone) {
      uploadDropZone.classList.remove("hidden");
      // Also add a full-screen overlay for file uploads
      mainContent.classList.add("upload-active");
    }
  }

  function hideUploadDropZone() {
    if (uploadDropZone) {
      uploadDropZone.classList.add("hidden");
      mainContent.classList.remove("upload-active");
    }
  }

  // Hide upload drop zone - we'll use the global drop zone for moving items
  function checkEmptyFolder() {
    // Always hide the upload drop zone - we'll use the global drop zone for moving items
    if (uploadDropZone) {
      uploadDropZone.classList.add("hidden");
    }
  }

  // Override updateView to check for empty folders
  const originalUpdateView = updateView;
  updateView = function () {
    originalUpdateView();
    checkEmptyFolder();
  };

  // Initial render
  if (folderTree && documentList) {
    updateView();
  }
});
